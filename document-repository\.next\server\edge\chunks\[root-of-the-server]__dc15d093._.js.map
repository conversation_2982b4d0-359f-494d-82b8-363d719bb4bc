{"version": 3, "sources": [], "sections": [{"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { withA<PERSON> } from \"next-auth/middleware\"\nimport { NextResponse } from \"next/server\"\n\nexport default withAuth(\n  function middleware(req) {\n    // Check if user is trying to access admin routes\n    if (req.nextUrl.pathname.startsWith(\"/admin\")) {\n      if (req.nextauth.token?.role !== \"ADMIN\") {\n        return NextResponse.redirect(new URL(\"/\", req.url))\n      }\n    }\n\n    // Check if user is trying to access upload page\n    if (req.nextUrl.pathname.startsWith(\"/upload\")) {\n      if (!req.nextauth.token) {\n        return NextResponse.redirect(new URL(\"/\", req.url))\n      }\n    }\n\n    // Add security headers\n    const response = NextResponse.next()\n    \n    // Prevent clickjacking\n    response.headers.set('X-Frame-Options', 'DENY')\n    \n    // Prevent MIME type sniffing\n    response.headers.set('X-Content-Type-Options', 'nosniff')\n    \n    // Enable XSS protection\n    response.headers.set('X-XSS-Protection', '1; mode=block')\n    \n    // Referrer policy\n    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')\n    \n    return response\n  },\n  {\n    callbacks: {\n      authorized: ({ token, req }) => {\n        // Allow access to public routes\n        if (req.nextUrl.pathname === \"/\" || \n            req.nextUrl.pathname.startsWith(\"/api/auth\")) {\n          return true\n        }\n        \n        // Require authentication for protected routes\n        return !!token\n      },\n    },\n  }\n)\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - api/auth (authentication routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */\n    \"/((?!api/auth|_next/static|_next/image|favicon.ico|public).*)\",\n  ],\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;uCAEe,CAAA,GAAA,kJAAA,CAAA,WAAQ,AAAD,EACpB,SAAS,WAAW,GAAG;IACrB,iDAAiD;IACjD,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,WAAW;QAC7C,IAAI,IAAI,QAAQ,CAAC,KAAK,EAAE,SAAS,SAAS;YACxC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,KAAK,IAAI,GAAG;QACnD;IACF;IAEA,gDAAgD;IAChD,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,YAAY;QAC9C,IAAI,CAAC,IAAI,QAAQ,CAAC,KAAK,EAAE;YACvB,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,KAAK,IAAI,GAAG;QACnD;IACF;IAEA,uBAAuB;IACvB,MAAM,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI;IAElC,uBAAuB;IACvB,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;IAExC,6BAA6B;IAC7B,SAAS,OAAO,CAAC,GAAG,CAAC,0BAA0B;IAE/C,wBAAwB;IACxB,SAAS,OAAO,CAAC,GAAG,CAAC,oBAAoB;IAEzC,kBAAkB;IAClB,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;IAExC,OAAO;AACT,GACA;IACE,WAAW;QACT,YAAY,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE;YACzB,gCAAgC;YAChC,IAAI,IAAI,OAAO,CAAC,QAAQ,KAAK,OACzB,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,cAAc;gBAChD,OAAO;YACT;YAEA,8CAA8C;YAC9C,OAAO,CAAC,CAAC;QACX;IACF;AACF;AAGK,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;;KAOC,GACD;KACD;AACH"}}]}
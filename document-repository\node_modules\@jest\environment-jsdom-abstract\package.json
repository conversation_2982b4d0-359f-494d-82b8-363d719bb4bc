{"name": "@jest/environment-jsdom-abstract", "version": "30.0.4", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-environment-jsdom-abstract"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@jest/environment": "30.0.4", "@jest/fake-timers": "30.0.4", "@jest/types": "30.0.1", "@types/jsdom": "^21.1.7", "@types/node": "*", "jest-mock": "30.0.2", "jest-util": "30.0.2"}, "devDependencies": {"jsdom": "^26.1.0"}, "peerDependencies": {"canvas": "^3.0.0", "jsdom": "*"}, "peerDependenciesMeta": {"canvas": {"optional": true}}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "f4296d2bc85c1405f84ddf613a25d0bc3766b7e5"}
'use client'

import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import FileUpload from '@/components/FileUpload'
import { ArrowLeft, CheckCircle } from 'lucide-react'
import Link from 'next/link'

export default function UploadPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([])

  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!session) {
    router.push('/')
    return null
  }

  const handleUploadComplete = (file: any) => {
    setUploadedFiles(prev => [...prev, file])
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link
          href="/"
          className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Documents
        </Link>
      </div>

      <div>
        <h1 className="text-2xl font-bold text-gray-900">Upload Documents</h1>
        <p className="text-gray-600 mt-1">
          Upload your files to Google Drive with automatic organization and tracking
        </p>
      </div>

      {/* Upload Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <FileUpload onUploadComplete={handleUploadComplete} />
      </div>

      {/* Recently Uploaded Files */}
      {uploadedFiles.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
            Recently Uploaded
          </h2>
          <div className="space-y-3">
            {uploadedFiles.map((file, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                <div>
                  <p className="font-medium text-gray-900">{file.originalName}</p>
                  <p className="text-sm text-gray-600">
                    Uploaded successfully • {new Date(file.uploadedAt).toLocaleString()}
                  </p>
                </div>
                <a
                  href={file.googleDriveUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                >
                  View in Drive
                </a>
              </div>
            ))}
          </div>
          <div className="mt-4 pt-4 border-t border-gray-200">
            <Link
              href="/"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              View All Documents
            </Link>
          </div>
        </div>
      )}

      {/* Upload Guidelines */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-3">Upload Guidelines</h3>
        <ul className="space-y-2 text-blue-800">
          <li>• Maximum file size: 10MB per file</li>
          <li>• Supported formats: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT, Images</li>
          <li>• Files are automatically uploaded to your organization's Google Drive</li>
          <li>• All uploads are logged and tracked for security</li>
          <li>• Add descriptions and tags to help organize your documents</li>
        </ul>
      </div>
    </div>
  )
}

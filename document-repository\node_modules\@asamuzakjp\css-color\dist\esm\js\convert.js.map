{"version": 3, "file": "convert.js", "sources": ["../../../src/js/convert.ts"], "sourcesContent": ["/**\n * convert\n */\n\nimport {\n  CacheItem,\n  NullObject,\n  createCacheKey,\n  getCache,\n  setCache\n} from './cache';\nimport {\n  convertColorToHsl,\n  convertColorToHwb,\n  convertColorToLab,\n  convertColorToLch,\n  convertColorToOklab,\n  convertColorToOklch,\n  convertColorToRgb,\n  numberToHexString,\n  parseColorFunc,\n  parseColorValue\n} from './color';\nimport { isString } from './common';\nimport { cssCalc } from './css-calc';\nimport { resolveVar } from './css-var';\nimport { resolveRelativeColor } from './relative-color';\nimport { resolveColor } from './resolve';\nimport { ColorChannels, ComputedColorChannels, Options } from './typedef';\n\n/* constants */\nimport { SYN_FN_CALC, SYN_FN_REL, SYN_FN_VAR, VAL_COMP } from './constant';\nconst NAMESPACE = 'convert';\n\n/* regexp */\nconst REG_FN_CALC = new RegExp(SYN_FN_CALC);\nconst REG_FN_REL = new RegExp(SYN_FN_REL);\nconst REG_FN_VAR = new RegExp(SYN_FN_VAR);\n\n/**\n * pre process\n * @param value - CSS color value\n * @param [opt] - options\n * @returns value\n */\nexport const preProcess = (\n  value: string,\n  opt: Options = {}\n): string | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n    if (!value) {\n      return new NullObject();\n    }\n  } else {\n    return new NullObject();\n  }\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'preProcess',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    if (cachedResult.isNull) {\n      return cachedResult as NullObject;\n    }\n    return cachedResult.item as string;\n  }\n  if (REG_FN_VAR.test(value)) {\n    const resolvedValue = resolveVar(value, opt);\n    if (isString(resolvedValue)) {\n      value = resolvedValue;\n    } else {\n      setCache(cacheKey, null);\n      return new NullObject();\n    }\n  }\n  if (REG_FN_REL.test(value)) {\n    const resolvedValue = resolveRelativeColor(value, opt);\n    if (isString(resolvedValue)) {\n      value = resolvedValue;\n    } else {\n      setCache(cacheKey, null);\n      return new NullObject();\n    }\n  } else if (REG_FN_CALC.test(value)) {\n    value = cssCalc(value, opt);\n  }\n  if (value.startsWith('color-mix')) {\n    const clonedOpt = structuredClone(opt);\n    clonedOpt.format = VAL_COMP;\n    clonedOpt.nullable = true;\n    const resolvedValue = resolveColor(value, clonedOpt);\n    setCache(cacheKey, resolvedValue);\n    return resolvedValue;\n  }\n  setCache(cacheKey, value);\n  return value;\n};\n\n/**\n * convert number to hex string\n * @param value - numeric value\n * @returns hex string: 00..ff\n */\nexport const numberToHex = (value: number): string => {\n  const hex = numberToHexString(value);\n  return hex;\n};\n\n/**\n * convert color to hex\n * @param value - CSS color value\n * @param [opt] - options\n * @param [opt.alpha] - enable alpha channel\n * @returns #rrggbb | #rrggbbaa | null\n */\nexport const colorToHex = (value: string, opt: Options = {}): string | null => {\n  if (isString(value)) {\n    const resolvedValue = preProcess(value, opt);\n    if (resolvedValue instanceof NullObject) {\n      return null;\n    }\n    value = resolvedValue.toLowerCase();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { alpha = false } = opt;\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'colorToHex',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    if (cachedResult.isNull) {\n      return null;\n    }\n    return cachedResult.item as string;\n  }\n  let hex;\n  opt.nullable = true;\n  if (alpha) {\n    opt.format = 'hexAlpha';\n    hex = resolveColor(value, opt);\n  } else {\n    opt.format = 'hex';\n    hex = resolveColor(value, opt);\n  }\n  if (isString(hex)) {\n    setCache(cacheKey, hex);\n    return hex;\n  }\n  setCache(cacheKey, null);\n  return null;\n};\n\n/**\n * convert color to hsl\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels - [h, s, l, alpha]\n */\nexport const colorToHsl = (value: string, opt: Options = {}): ColorChannels => {\n  if (isString(value)) {\n    const resolvedValue = preProcess(value, opt);\n    if (resolvedValue instanceof NullObject) {\n      return [0, 0, 0, 0];\n    }\n    value = resolvedValue.toLowerCase();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'colorToHsl',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    return cachedResult.item as ColorChannels;\n  }\n  opt.format = 'hsl';\n  const hsl = convertColorToHsl(value, opt) as ColorChannels;\n  setCache(cacheKey, hsl);\n  return hsl;\n};\n\n/**\n * convert color to hwb\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels - [h, w, b, alpha]\n */\nexport const colorToHwb = (value: string, opt: Options = {}): ColorChannels => {\n  if (isString(value)) {\n    const resolvedValue = preProcess(value, opt);\n    if (resolvedValue instanceof NullObject) {\n      return [0, 0, 0, 0];\n    }\n    value = resolvedValue.toLowerCase();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'colorToHwb',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    return cachedResult.item as ColorChannels;\n  }\n  opt.format = 'hwb';\n  const hwb = convertColorToHwb(value, opt) as ColorChannels;\n  setCache(cacheKey, hwb);\n  return hwb;\n};\n\n/**\n * convert color to lab\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels - [l, a, b, alpha]\n */\nexport const colorToLab = (value: string, opt: Options = {}): ColorChannels => {\n  if (isString(value)) {\n    const resolvedValue = preProcess(value, opt);\n    if (resolvedValue instanceof NullObject) {\n      return [0, 0, 0, 0];\n    }\n    value = resolvedValue.toLowerCase();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'colorToLab',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    return cachedResult.item as ColorChannels;\n  }\n  const lab = convertColorToLab(value, opt) as ColorChannels;\n  setCache(cacheKey, lab);\n  return lab;\n};\n\n/**\n * convert color to lch\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels - [l, c, h, alpha]\n */\nexport const colorToLch = (value: string, opt: Options = {}): ColorChannels => {\n  if (isString(value)) {\n    const resolvedValue = preProcess(value, opt);\n    if (resolvedValue instanceof NullObject) {\n      return [0, 0, 0, 0];\n    }\n    value = resolvedValue.toLowerCase();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'colorToLch',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    return cachedResult.item as ColorChannels;\n  }\n  const lch = convertColorToLch(value, opt) as ColorChannels;\n  setCache(cacheKey, lch);\n  return lch;\n};\n\n/**\n * convert color to oklab\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels - [l, a, b, alpha]\n */\nexport const colorToOklab = (\n  value: string,\n  opt: Options = {}\n): ColorChannels => {\n  if (isString(value)) {\n    const resolvedValue = preProcess(value, opt);\n    if (resolvedValue instanceof NullObject) {\n      return [0, 0, 0, 0];\n    }\n    value = resolvedValue.toLowerCase();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'colorToOklab',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    return cachedResult.item as ColorChannels;\n  }\n  const lab = convertColorToOklab(value, opt) as ColorChannels;\n  setCache(cacheKey, lab);\n  return lab;\n};\n\n/**\n * convert color to oklch\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels - [l, c, h, alpha]\n */\nexport const colorToOklch = (\n  value: string,\n  opt: Options = {}\n): ColorChannels => {\n  if (isString(value)) {\n    const resolvedValue = preProcess(value, opt);\n    if (resolvedValue instanceof NullObject) {\n      return [0, 0, 0, 0];\n    }\n    value = resolvedValue.toLowerCase();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'colorToOklch',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    return cachedResult.item as ColorChannels;\n  }\n  const lch = convertColorToOklch(value, opt) as ColorChannels;\n  setCache(cacheKey, lch);\n  return lch;\n};\n\n/**\n * convert color to rgb\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels - [r, g, b, alpha]\n */\nexport const colorToRgb = (value: string, opt: Options = {}): ColorChannels => {\n  if (isString(value)) {\n    const resolvedValue = preProcess(value, opt);\n    if (resolvedValue instanceof NullObject) {\n      return [0, 0, 0, 0];\n    }\n    value = resolvedValue.toLowerCase();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'colorToRgb',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    return cachedResult.item as ColorChannels;\n  }\n  const rgb = convertColorToRgb(value, opt) as ColorChannels;\n  setCache(cacheKey, rgb);\n  return rgb;\n};\n\n/**\n * convert color to xyz\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels - [x, y, z, alpha]\n */\nexport const colorToXyz = (value: string, opt: Options = {}): ColorChannels => {\n  if (isString(value)) {\n    const resolvedValue = preProcess(value, opt);\n    if (resolvedValue instanceof NullObject) {\n      return [0, 0, 0, 0];\n    }\n    value = resolvedValue.toLowerCase();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'colorToXyz',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    return cachedResult.item as ColorChannels;\n  }\n  let xyz;\n  if (value.startsWith('color(')) {\n    [, ...xyz] = parseColorFunc(value, opt) as ComputedColorChannels;\n  } else {\n    [, ...xyz] = parseColorValue(value, opt) as ComputedColorChannels;\n  }\n  setCache(cacheKey, xyz);\n  return xyz as ColorChannels;\n};\n\n/**\n * convert color to xyz-d50\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels - [x, y, z, alpha]\n */\nexport const colorToXyzD50 = (\n  value: string,\n  opt: Options = {}\n): ColorChannels => {\n  opt.d50 = true;\n  return colorToXyz(value, opt);\n};\n\n/* convert */\nexport const convert = {\n  colorToHex,\n  colorToHsl,\n  colorToHwb,\n  colorToLab,\n  colorToLch,\n  colorToOklab,\n  colorToOklch,\n  colorToRgb,\n  colorToXyz,\n  colorToXyzD50,\n  numberToHex\n};\n"], "names": [], "mappings": ";;;;;;;;AAgCA,MAAM,YAAY;AAGlB,MAAM,cAAc,IAAI,OAAO,WAAW;AAC1C,MAAM,aAAa,IAAI,OAAO,UAAU;AACxC,MAAM,aAAa,IAAI,OAAO,UAAU;AAQjC,MAAM,aAAa,CACxB,OACA,MAAe,OACS;AACpB,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AACnB,QAAI,CAAC,OAAO;AACV,aAAO,IAAI,WAAW;AAAA,IAAA;AAAA,EACxB,OACK;AACL,WAAO,IAAI,WAAW;AAAA,EAAA;AAExB,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAW;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACM,QAAA,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,QAAI,aAAa,QAAQ;AAChB,aAAA;AAAA,IAAA;AAET,WAAO,aAAa;AAAA,EAAA;AAElB,MAAA,WAAW,KAAK,KAAK,GAAG;AACpB,UAAA,gBAAgB,WAAW,OAAO,GAAG;AACvC,QAAA,SAAS,aAAa,GAAG;AACnB,cAAA;AAAA,IAAA,OACH;AACL,eAAS,UAAU,IAAI;AACvB,aAAO,IAAI,WAAW;AAAA,IAAA;AAAA,EACxB;AAEE,MAAA,WAAW,KAAK,KAAK,GAAG;AACpB,UAAA,gBAAgB,qBAAqB,OAAO,GAAG;AACjD,QAAA,SAAS,aAAa,GAAG;AACnB,cAAA;AAAA,IAAA,OACH;AACL,eAAS,UAAU,IAAI;AACvB,aAAO,IAAI,WAAW;AAAA,IAAA;AAAA,EAEf,WAAA,YAAY,KAAK,KAAK,GAAG;AAC1B,YAAA,QAAQ,OAAO,GAAG;AAAA,EAAA;AAExB,MAAA,MAAM,WAAW,WAAW,GAAG;AAC3B,UAAA,YAAY,gBAAgB,GAAG;AACrC,cAAU,SAAS;AACnB,cAAU,WAAW;AACf,UAAA,gBAAgB,aAAa,OAAO,SAAS;AACnD,aAAS,UAAU,aAAa;AACzB,WAAA;AAAA,EAAA;AAET,WAAS,UAAU,KAAK;AACjB,SAAA;AACT;AAOa,MAAA,cAAc,CAAC,UAA0B;AAC9C,QAAA,MAAM,kBAAkB,KAAK;AAC5B,SAAA;AACT;AASO,MAAM,aAAa,CAAC,OAAe,MAAe,OAAsB;AACzE,MAAA,SAAS,KAAK,GAAG;AACb,UAAA,gBAAgB,WAAW,OAAO,GAAG;AAC3C,QAAI,yBAAyB,YAAY;AAChC,aAAA;AAAA,IAAA;AAET,YAAQ,cAAc,YAAY;AAAA,EAAA,OAC7B;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,QAAQ,MAAA,IAAU;AAC1B,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAW;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACM,QAAA,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,QAAI,aAAa,QAAQ;AAChB,aAAA;AAAA,IAAA;AAET,WAAO,aAAa;AAAA,EAAA;AAElB,MAAA;AACJ,MAAI,WAAW;AACf,MAAI,OAAO;AACT,QAAI,SAAS;AACP,UAAA,aAAa,OAAO,GAAG;AAAA,EAAA,OACxB;AACL,QAAI,SAAS;AACP,UAAA,aAAa,OAAO,GAAG;AAAA,EAAA;AAE3B,MAAA,SAAS,GAAG,GAAG;AACjB,aAAS,UAAU,GAAG;AACf,WAAA;AAAA,EAAA;AAET,WAAS,UAAU,IAAI;AAChB,SAAA;AACT;AAQO,MAAM,aAAa,CAAC,OAAe,MAAe,OAAsB;AACzE,MAAA,SAAS,KAAK,GAAG;AACb,UAAA,gBAAgB,WAAW,OAAO,GAAG;AAC3C,QAAI,yBAAyB,YAAY;AACvC,aAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,IAAA;AAEpB,YAAQ,cAAc,YAAY;AAAA,EAAA,OAC7B;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAEjD,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAW;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACM,QAAA,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,WAAO,aAAa;AAAA,EAAA;AAEtB,MAAI,SAAS;AACP,QAAA,MAAM,kBAAkB,OAAO,GAAG;AACxC,WAAS,UAAU,GAAG;AACf,SAAA;AACT;AAQO,MAAM,aAAa,CAAC,OAAe,MAAe,OAAsB;AACzE,MAAA,SAAS,KAAK,GAAG;AACb,UAAA,gBAAgB,WAAW,OAAO,GAAG;AAC3C,QAAI,yBAAyB,YAAY;AACvC,aAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,IAAA;AAEpB,YAAQ,cAAc,YAAY;AAAA,EAAA,OAC7B;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAEjD,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAW;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACM,QAAA,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,WAAO,aAAa;AAAA,EAAA;AAEtB,MAAI,SAAS;AACP,QAAA,MAAM,kBAAkB,OAAO,GAAG;AACxC,WAAS,UAAU,GAAG;AACf,SAAA;AACT;AAQO,MAAM,aAAa,CAAC,OAAe,MAAe,OAAsB;AACzE,MAAA,SAAS,KAAK,GAAG;AACb,UAAA,gBAAgB,WAAW,OAAO,GAAG;AAC3C,QAAI,yBAAyB,YAAY;AACvC,aAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,IAAA;AAEpB,YAAQ,cAAc,YAAY;AAAA,EAAA,OAC7B;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAEjD,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAW;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACM,QAAA,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,WAAO,aAAa;AAAA,EAAA;AAEhB,QAAA,MAAM,kBAAkB,OAAO,GAAG;AACxC,WAAS,UAAU,GAAG;AACf,SAAA;AACT;AAQO,MAAM,aAAa,CAAC,OAAe,MAAe,OAAsB;AACzE,MAAA,SAAS,KAAK,GAAG;AACb,UAAA,gBAAgB,WAAW,OAAO,GAAG;AAC3C,QAAI,yBAAyB,YAAY;AACvC,aAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,IAAA;AAEpB,YAAQ,cAAc,YAAY;AAAA,EAAA,OAC7B;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAEjD,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAW;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACM,QAAA,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,WAAO,aAAa;AAAA,EAAA;AAEhB,QAAA,MAAM,kBAAkB,OAAO,GAAG;AACxC,WAAS,UAAU,GAAG;AACf,SAAA;AACT;AAQO,MAAM,eAAe,CAC1B,OACA,MAAe,OACG;AACd,MAAA,SAAS,KAAK,GAAG;AACb,UAAA,gBAAgB,WAAW,OAAO,GAAG;AAC3C,QAAI,yBAAyB,YAAY;AACvC,aAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,IAAA;AAEpB,YAAQ,cAAc,YAAY;AAAA,EAAA,OAC7B;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAEjD,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAW;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACM,QAAA,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,WAAO,aAAa;AAAA,EAAA;AAEhB,QAAA,MAAM,oBAAoB,OAAO,GAAG;AAC1C,WAAS,UAAU,GAAG;AACf,SAAA;AACT;AAQO,MAAM,eAAe,CAC1B,OACA,MAAe,OACG;AACd,MAAA,SAAS,KAAK,GAAG;AACb,UAAA,gBAAgB,WAAW,OAAO,GAAG;AAC3C,QAAI,yBAAyB,YAAY;AACvC,aAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,IAAA;AAEpB,YAAQ,cAAc,YAAY;AAAA,EAAA,OAC7B;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAEjD,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAW;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACM,QAAA,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,WAAO,aAAa;AAAA,EAAA;AAEhB,QAAA,MAAM,oBAAoB,OAAO,GAAG;AAC1C,WAAS,UAAU,GAAG;AACf,SAAA;AACT;AAQO,MAAM,aAAa,CAAC,OAAe,MAAe,OAAsB;AACzE,MAAA,SAAS,KAAK,GAAG;AACb,UAAA,gBAAgB,WAAW,OAAO,GAAG;AAC3C,QAAI,yBAAyB,YAAY;AACvC,aAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,IAAA;AAEpB,YAAQ,cAAc,YAAY;AAAA,EAAA,OAC7B;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAEjD,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAW;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACM,QAAA,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,WAAO,aAAa;AAAA,EAAA;AAEhB,QAAA,MAAM,kBAAkB,OAAO,GAAG;AACxC,WAAS,UAAU,GAAG;AACf,SAAA;AACT;AAQO,MAAM,aAAa,CAAC,OAAe,MAAe,OAAsB;AACzE,MAAA,SAAS,KAAK,GAAG;AACb,UAAA,gBAAgB,WAAW,OAAO,GAAG;AAC3C,QAAI,yBAAyB,YAAY;AACvC,aAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,IAAA;AAEpB,YAAQ,cAAc,YAAY;AAAA,EAAA,OAC7B;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAEjD,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAW;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACM,QAAA,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,WAAO,aAAa;AAAA,EAAA;AAElB,MAAA;AACA,MAAA,MAAM,WAAW,QAAQ,GAAG;AAC9B,KAAA,EAAG,GAAG,GAAG,IAAI,eAAe,OAAO,GAAG;AAAA,EAAA,OACjC;AACL,KAAA,EAAG,GAAG,GAAG,IAAI,gBAAgB,OAAO,GAAG;AAAA,EAAA;AAEzC,WAAS,UAAU,GAAG;AACf,SAAA;AACT;AAQO,MAAM,gBAAgB,CAC3B,OACA,MAAe,OACG;AAClB,MAAI,MAAM;AACH,SAAA,WAAW,OAAO,GAAG;AAC9B;AAGO,MAAM,UAAU;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;"}
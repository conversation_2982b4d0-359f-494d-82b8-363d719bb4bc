{"version": 3, "file": "index.js", "sources": ["../../src/domain.ts", "../../src/domain-without-suffix.ts", "../../src/extract-hostname.ts", "../../src/is-ip.ts", "../../src/is-valid.ts", "../../src/options.ts", "../../src/subdomain.ts", "../../src/factory.ts", "../../src/lookup/fast-path.ts"], "sourcesContent": ["import { IOptions } from './options';\n\n/**\n * Check if `vhost` is a valid suffix of `hostname` (top-domain)\n *\n * It means that `vhost` needs to be a suffix of `hostname` and we then need to\n * make sure that: either they are equal, or the character preceding `vhost` in\n * `hostname` is a '.' (it should not be a partial label).\n *\n * * hostname = 'not.evil.com' and vhost = 'vil.com'      => not ok\n * * hostname = 'not.evil.com' and vhost = 'evil.com'     => ok\n * * hostname = 'not.evil.com' and vhost = 'not.evil.com' => ok\n */\nfunction shareSameDomainSuffix(hostname: string, vhost: string): boolean {\n  if (hostname.endsWith(vhost)) {\n    return (\n      hostname.length === vhost.length ||\n      hostname[hostname.length - vhost.length - 1] === '.'\n    );\n  }\n\n  return false;\n}\n\n/**\n * Given a hostname and its public suffix, extract the general domain.\n */\nfunction extractDomainWithSuffix(\n  hostname: string,\n  publicSuffix: string,\n): string {\n  // Locate the index of the last '.' in the part of the `hostname` preceding\n  // the public suffix.\n  //\n  // examples:\n  //   1. not.evil.co.uk  => evil.co.uk\n  //         ^    ^\n  //         |    | start of public suffix\n  //         | index of the last dot\n  //\n  //   2. example.co.uk   => example.co.uk\n  //     ^       ^\n  //     |       | start of public suffix\n  //     |\n  //     | (-1) no dot found before the public suffix\n  const publicSuffixIndex = hostname.length - publicSuffix.length - 2;\n  const lastDotBeforeSuffixIndex = hostname.lastIndexOf('.', publicSuffixIndex);\n\n  // No '.' found, then `hostname` is the general domain (no sub-domain)\n  if (lastDotBeforeSuffixIndex === -1) {\n    return hostname;\n  }\n\n  // Extract the part between the last '.'\n  return hostname.slice(lastDotBeforeSuffixIndex + 1);\n}\n\n/**\n * Detects the domain based on rules and upon and a host string\n */\nexport default function getDomain(\n  suffix: string,\n  hostname: string,\n  options: IOptions,\n): string | null {\n  // Check if `hostname` ends with a member of `validHosts`.\n  if (options.validHosts !== null) {\n    const validHosts = options.validHosts;\n    for (const vhost of validHosts) {\n      if (/*@__INLINE__*/ shareSameDomainSuffix(hostname, vhost)) {\n        return vhost;\n      }\n    }\n  }\n\n  let numberOfLeadingDots = 0;\n  if (hostname.startsWith('.')) {\n    while (\n      numberOfLeadingDots < hostname.length &&\n      hostname[numberOfLeadingDots] === '.'\n    ) {\n      numberOfLeadingDots += 1;\n    }\n  }\n\n  // If `hostname` is a valid public suffix, then there is no domain to return.\n  // Since we already know that `getPublicSuffix` returns a suffix of `hostname`\n  // there is no need to perform a string comparison and we only compare the\n  // size.\n  if (suffix.length === hostname.length - numberOfLeadingDots) {\n    return null;\n  }\n\n  // To extract the general domain, we start by identifying the public suffix\n  // (if any), then consider the domain to be the public suffix with one added\n  // level of depth. (e.g.: if hostname is `not.evil.co.uk` and public suffix:\n  // `co.uk`, then we take one more level: `evil`, giving the final result:\n  // `evil.co.uk`).\n  return /*@__INLINE__*/ extractDomainWithSuffix(hostname, suffix);\n}\n", "/**\n * Return the part of domain without suffix.\n *\n * Example: for domain 'foo.com', the result would be 'foo'.\n */\nexport default function getDomainWithoutSuffix(\n  domain: string,\n  suffix: string,\n): string {\n  // Note: here `domain` and `suffix` cannot have the same length because in\n  // this case we set `domain` to `null` instead. It is thus safe to assume\n  // that `suffix` is shorter than `domain`.\n  return domain.slice(0, -suffix.length - 1);\n}\n", "/**\n * @param url - URL we want to extract a hostname from.\n * @param urlIsValidHostname - hint from caller; true if `url` is already a valid hostname.\n */\nexport default function extractHostname(\n  url: string,\n  urlIsValidHostname: boolean,\n): string | null {\n  let start = 0;\n  let end: number = url.length;\n  let hasUpper = false;\n\n  // If url is not already a valid hostname, then try to extract hostname.\n  if (!urlIsValidHostname) {\n    // Special handling of data URLs\n    if (url.startsWith('data:')) {\n      return null;\n    }\n\n    // Trim leading spaces\n    while (start < url.length && url.charCodeAt(start) <= 32) {\n      start += 1;\n    }\n\n    // Trim trailing spaces\n    while (end > start + 1 && url.charCodeAt(end - 1) <= 32) {\n      end -= 1;\n    }\n\n    // Skip scheme.\n    if (\n      url.charCodeAt(start) === 47 /* '/' */ &&\n      url.charCodeAt(start + 1) === 47 /* '/' */\n    ) {\n      start += 2;\n    } else {\n      const indexOfProtocol = url.indexOf(':/', start);\n      if (indexOfProtocol !== -1) {\n        // Implement fast-path for common protocols. We expect most protocols\n        // should be one of these 4 and thus we will not need to perform the\n        // more expansive validity check most of the time.\n        const protocolSize = indexOfProtocol - start;\n        const c0 = url.charCodeAt(start);\n        const c1 = url.charCodeAt(start + 1);\n        const c2 = url.charCodeAt(start + 2);\n        const c3 = url.charCodeAt(start + 3);\n        const c4 = url.charCodeAt(start + 4);\n\n        if (\n          protocolSize === 5 &&\n          c0 === 104 /* 'h' */ &&\n          c1 === 116 /* 't' */ &&\n          c2 === 116 /* 't' */ &&\n          c3 === 112 /* 'p' */ &&\n          c4 === 115 /* 's' */\n        ) {\n          // https\n        } else if (\n          protocolSize === 4 &&\n          c0 === 104 /* 'h' */ &&\n          c1 === 116 /* 't' */ &&\n          c2 === 116 /* 't' */ &&\n          c3 === 112 /* 'p' */\n        ) {\n          // http\n        } else if (\n          protocolSize === 3 &&\n          c0 === 119 /* 'w' */ &&\n          c1 === 115 /* 's' */ &&\n          c2 === 115 /* 's' */\n        ) {\n          // wss\n        } else if (\n          protocolSize === 2 &&\n          c0 === 119 /* 'w' */ &&\n          c1 === 115 /* 's' */\n        ) {\n          // ws\n        } else {\n          // Check that scheme is valid\n          for (let i = start; i < indexOfProtocol; i += 1) {\n            const lowerCaseCode = url.charCodeAt(i) | 32;\n            if (\n              !(\n                (\n                  (lowerCaseCode >= 97 && lowerCaseCode <= 122) || // [a, z]\n                  (lowerCaseCode >= 48 && lowerCaseCode <= 57) || // [0, 9]\n                  lowerCaseCode === 46 || // '.'\n                  lowerCaseCode === 45 || // '-'\n                  lowerCaseCode === 43\n                ) // '+'\n              )\n            ) {\n              return null;\n            }\n          }\n        }\n\n        // Skip 0, 1 or more '/' after ':/'\n        start = indexOfProtocol + 2;\n        while (url.charCodeAt(start) === 47 /* '/' */) {\n          start += 1;\n        }\n      }\n    }\n\n    // Detect first occurrence of '/', '?' or '#'. We also keep track of the\n    // last occurrence of '@', ']' or ':' to speed-up subsequent parsing of\n    // (respectively), identifier, ipv6 or port.\n    let indexOfIdentifier = -1;\n    let indexOfClosingBracket = -1;\n    let indexOfPort = -1;\n    for (let i = start; i < end; i += 1) {\n      const code: number = url.charCodeAt(i);\n      if (\n        code === 35 || // '#'\n        code === 47 || // '/'\n        code === 63 // '?'\n      ) {\n        end = i;\n        break;\n      } else if (code === 64) {\n        // '@'\n        indexOfIdentifier = i;\n      } else if (code === 93) {\n        // ']'\n        indexOfClosingBracket = i;\n      } else if (code === 58) {\n        // ':'\n        indexOfPort = i;\n      } else if (code >= 65 && code <= 90) {\n        hasUpper = true;\n      }\n    }\n\n    // Detect identifier: '@'\n    if (\n      indexOfIdentifier !== -1 &&\n      indexOfIdentifier > start &&\n      indexOfIdentifier < end\n    ) {\n      start = indexOfIdentifier + 1;\n    }\n\n    // Handle ipv6 addresses\n    if (url.charCodeAt(start) === 91 /* '[' */) {\n      if (indexOfClosingBracket !== -1) {\n        return url.slice(start + 1, indexOfClosingBracket).toLowerCase();\n      }\n      return null;\n    } else if (indexOfPort !== -1 && indexOfPort > start && indexOfPort < end) {\n      // Detect port: ':'\n      end = indexOfPort;\n    }\n  }\n\n  // Trim trailing dots\n  while (end > start + 1 && url.charCodeAt(end - 1) === 46 /* '.' */) {\n    end -= 1;\n  }\n\n  const hostname: string =\n    start !== 0 || end !== url.length ? url.slice(start, end) : url;\n\n  if (hasUpper) {\n    return hostname.toLowerCase();\n  }\n\n  return hostname;\n}\n", "/**\n * Check if a hostname is an IP. You should be aware that this only works\n * because `hostname` is already garanteed to be a valid hostname!\n */\nfunction isProbablyIpv4(hostname: string): boolean {\n  // Cannot be shorted than *******\n  if (hostname.length < 7) {\n    return false;\n  }\n\n  // Cannot be longer than: ***************\n  if (hostname.length > 15) {\n    return false;\n  }\n\n  let numberOfDots = 0;\n\n  for (let i = 0; i < hostname.length; i += 1) {\n    const code = hostname.charCodeAt(i);\n\n    if (code === 46 /* '.' */) {\n      numberOfDots += 1;\n    } else if (code < 48 /* '0' */ || code > 57 /* '9' */) {\n      return false;\n    }\n  }\n\n  return (\n    numberOfDots === 3 &&\n    hostname.charCodeAt(0) !== 46 /* '.' */ &&\n    hostname.charCodeAt(hostname.length - 1) !== 46 /* '.' */\n  );\n}\n\n/**\n * Similar to isProbablyIpv4.\n */\nfunction isProbablyIpv6(hostname: string): boolean {\n  if (hostname.length < 3) {\n    return false;\n  }\n\n  let start = hostname.startsWith('[') ? 1 : 0;\n  let end = hostname.length;\n\n  if (hostname[end - 1] === ']') {\n    end -= 1;\n  }\n\n  // We only consider the maximum size of a normal IPV6. Note that this will\n  // fail on so-called \"IPv4 mapped IPv6 addresses\" but this is a corner-case\n  // and a proper validation library should be used for these.\n  if (end - start > 39) {\n    return false;\n  }\n\n  let hasColon = false;\n\n  for (; start < end; start += 1) {\n    const code = hostname.charCodeAt(start);\n\n    if (code === 58 /* ':' */) {\n      hasColon = true;\n    } else if (\n      !(\n        (\n          (code >= 48 && code <= 57) || // 0-9\n          (code >= 97 && code <= 102) || // a-f\n          (code >= 65 && code <= 90)\n        ) // A-F\n      )\n    ) {\n      return false;\n    }\n  }\n\n  return hasColon;\n}\n\n/**\n * Check if `hostname` is *probably* a valid ip addr (either ipv6 or ipv4).\n * This *will not* work on any string. We need `hostname` to be a valid\n * hostname.\n */\nexport default function isIp(hostname: string): boolean {\n  return isProbablyIpv6(hostname) || isProbablyIpv4(hostname);\n}\n", "/**\n * Implements fast shallow verification of hostnames. This does not perform a\n * struct check on the content of labels (classes of Unicode characters, etc.)\n * but instead check that the structure is valid (number of labels, length of\n * labels, etc.).\n *\n * If you need stricter validation, consider using an external library.\n */\n\nfunction isValidAscii(code: number): boolean {\n  return (\n    (code >= 97 && code <= 122) || (code >= 48 && code <= 57) || code > 127\n  );\n}\n\n/**\n * Check if a hostname string is valid. It's usually a preliminary check before\n * trying to use getDomain or anything else.\n *\n * Beware: it does not check if the TLD exists.\n */\nexport default function (hostname: string): boolean {\n  if (hostname.length > 255) {\n    return false;\n  }\n\n  if (hostname.length === 0) {\n    return false;\n  }\n\n  if (\n    /*@__INLINE__*/ !isValidAscii(hostname.charCodeAt(0)) &&\n    hostname.charCodeAt(0) !== 46 && // '.' (dot)\n    hostname.charCodeAt(0) !== 95 // '_' (underscore)\n  ) {\n    return false;\n  }\n\n  // Validate hostname according to RFC\n  let lastDotIndex = -1;\n  let lastCharCode = -1;\n  const len = hostname.length;\n\n  for (let i = 0; i < len; i += 1) {\n    const code = hostname.charCodeAt(i);\n    if (code === 46 /* '.' */) {\n      if (\n        // Check that previous label is < 63 bytes long (64 = 63 + '.')\n        i - lastDotIndex > 64 ||\n        // Check that previous character was not already a '.'\n        lastCharCode === 46 ||\n        // Check that the previous label does not end with a '-' (dash)\n        lastCharCode === 45 ||\n        // Check that the previous label does not end with a '_' (underscore)\n        lastCharCode === 95\n      ) {\n        return false;\n      }\n\n      lastDotIndex = i;\n    } else if (\n      !(/*@__INLINE__*/ (isValidAscii(code) || code === 45 || code === 95))\n    ) {\n      // Check if there is a forbidden character in the label\n      return false;\n    }\n\n    lastCharCode = code;\n  }\n\n  return (\n    // Check that last label is shorter than 63 chars\n    len - lastDotIndex - 1 <= 63 &&\n    // Check that the last character is an allowed trailing label character.\n    // Since we already checked that the char is a valid hostname character,\n    // we only need to check that it's different from '-'.\n    lastCharCode !== 45\n  );\n}\n", "export interface IOptions {\n  allowIcannDomains: boolean;\n  allowPrivateDomains: boolean;\n  detectIp: boolean;\n  extractHostname: boolean;\n  mixedInputs: boolean;\n  validHosts: string[] | null;\n  validateHostname: boolean;\n}\n\nfunction setDefaultsImpl({\n  allowIcannDomains = true,\n  allowPrivateDomains = false,\n  detectIp = true,\n  extractHostname = true,\n  mixedInputs = true,\n  validHosts = null,\n  validateHostname = true,\n}: Partial<IOptions>): IOptions {\n  return {\n    allowIcannDomains,\n    allowPrivateDomains,\n    detectIp,\n    extractHostname,\n    mixedInputs,\n    validHosts,\n    validateHostname,\n  };\n}\n\nconst DEFAULT_OPTIONS = /*@__INLINE__*/ setDefaultsImpl({});\n\nexport function setDefaults(options?: Partial<IOptions>): IOptions {\n  if (options === undefined) {\n    return DEFAULT_OPTIONS;\n  }\n\n  return /*@__INLINE__*/ setDefaultsImpl(options);\n}\n", "/**\n * Returns the subdomain of a hostname string\n */\nexport default function getSubdomain(hostname: string, domain: string): string {\n  // If `hostname` and `domain` are the same, then there is no sub-domain\n  if (domain.length === hostname.length) {\n    return '';\n  }\n\n  return hostname.slice(0, -domain.length - 1);\n}\n", "/**\n * Implement a factory allowing to plug different implementations of suffix\n * lookup (e.g.: using a trie or the packed hashes datastructures). This is used\n * and exposed in `tldts.ts` and `tldts-experimental.ts` bundle entrypoints.\n */\n\nimport getDomain from './domain';\nimport getDomainWithoutSuffix from './domain-without-suffix';\nimport extractHostname from './extract-hostname';\nimport isIp from './is-ip';\nimport isValidHostname from './is-valid';\nimport { IPublicSuffix, ISuffixLookupOptions } from './lookup/interface';\nimport { IOptions, setDefaults } from './options';\nimport getSubdomain from './subdomain';\n\nexport interface IResult {\n  // `hostname` is either a registered name (including but not limited to a\n  // hostname), or an IP address. IPv4 addresses must be in dot-decimal\n  // notation, and IPv6 addresses must be enclosed in brackets ([]). This is\n  // directly extracted from the input URL.\n  hostname: string | null;\n\n  // Is `hostname` an IP? (IPv4 or IPv6)\n  isIp: boolean | null;\n\n  // `hostname` split between subdomain, domain and its public suffix (if any)\n  subdomain: string | null;\n  domain: string | null;\n  publicSuffix: string | null;\n  domainWithoutSuffix: string | null;\n\n  // Specifies if `publicSuffix` comes from the ICANN or PRIVATE section of the list\n  isIcann: boolean | null;\n  isPrivate: boolean | null;\n}\n\nexport function getEmptyResult(): IResult {\n  return {\n    domain: null,\n    domainWithoutSuffix: null,\n    hostname: null,\n    isIcann: null,\n    isIp: null,\n    isPrivate: null,\n    publicSuffix: null,\n    subdomain: null,\n  };\n}\n\nexport function resetResult(result: IResult): void {\n  result.domain = null;\n  result.domainWithoutSuffix = null;\n  result.hostname = null;\n  result.isIcann = null;\n  result.isIp = null;\n  result.isPrivate = null;\n  result.publicSuffix = null;\n  result.subdomain = null;\n}\n\n// Flags representing steps in the `parse` function. They are used to implement\n// an early stop mechanism (simulating some form of laziness) to avoid doing\n// more work than necessary to perform a given action (e.g.: we don't need to\n// extract the domain and subdomain if we are only interested in public suffix).\nexport const enum FLAG {\n  HOSTNAME,\n  IS_VALID,\n  PUBLIC_SUFFIX,\n  DOMAIN,\n  SUB_DOMAIN,\n  ALL,\n}\n\nexport function parseImpl(\n  url: string,\n  step: FLAG,\n  suffixLookup: (\n    _1: string,\n    _2: ISuffixLookupOptions,\n    _3: IPublicSuffix,\n  ) => void,\n  partialOptions: Partial<IOptions>,\n  result: IResult,\n): IResult {\n  const options: IOptions = /*@__INLINE__*/ setDefaults(partialOptions);\n\n  // Very fast approximate check to make sure `url` is a string. This is needed\n  // because the library will not necessarily be used in a typed setup and\n  // values of arbitrary types might be given as argument.\n  if (typeof url !== 'string') {\n    return result;\n  }\n\n  // Extract hostname from `url` only if needed. This can be made optional\n  // using `options.extractHostname`. This option will typically be used\n  // whenever we are sure the inputs to `parse` are already hostnames and not\n  // arbitrary URLs.\n  //\n  // `mixedInput` allows to specify if we expect a mix of URLs and hostnames\n  // as input. If only hostnames are expected then `extractHostname` can be\n  // set to `false` to speed-up parsing. If only URLs are expected then\n  // `mixedInputs` can be set to `false`. The `mixedInputs` is only a hint\n  // and will not change the behavior of the library.\n  if (!options.extractHostname) {\n    result.hostname = url;\n  } else if (options.mixedInputs) {\n    result.hostname = extractHostname(url, isValidHostname(url));\n  } else {\n    result.hostname = extractHostname(url, false);\n  }\n\n  if (step === FLAG.HOSTNAME || result.hostname === null) {\n    return result;\n  }\n\n  // Check if `hostname` is a valid ip address\n  if (options.detectIp) {\n    result.isIp = isIp(result.hostname);\n    if (result.isIp) {\n      return result;\n    }\n  }\n\n  // Perform optional hostname validation. If hostname is not valid, no need to\n  // go further as there will be no valid domain or sub-domain.\n  if (\n    options.validateHostname &&\n    options.extractHostname &&\n    !isValidHostname(result.hostname)\n  ) {\n    result.hostname = null;\n    return result;\n  }\n\n  // Extract public suffix\n  suffixLookup(result.hostname, options, result);\n  if (step === FLAG.PUBLIC_SUFFIX || result.publicSuffix === null) {\n    return result;\n  }\n\n  // Extract domain\n  result.domain = getDomain(result.publicSuffix, result.hostname, options);\n  if (step === FLAG.DOMAIN || result.domain === null) {\n    return result;\n  }\n\n  // Extract subdomain\n  result.subdomain = getSubdomain(result.hostname, result.domain);\n  if (step === FLAG.SUB_DOMAIN) {\n    return result;\n  }\n\n  // Extract domain without suffix\n  result.domainWithoutSuffix = getDomainWithoutSuffix(\n    result.domain,\n    result.publicSuffix,\n  );\n\n  return result;\n}\n", "import { IPublicSuffix, ISuffixLookupOptions } from './interface';\n\nexport default function (\n  hostname: string,\n  options: ISuffixLookupOptions,\n  out: IPublicSuffix,\n): boolean {\n  // Fast path for very popular suffixes; this allows to by-pass lookup\n  // completely as well as any extra allocation or string manipulation.\n  if (!options.allowPrivateDomains && hostname.length > 3) {\n    const last: number = hostname.length - 1;\n    const c3: number = hostname.charCodeAt(last);\n    const c2: number = hostname.charCodeAt(last - 1);\n    const c1: number = hostname.charCodeAt(last - 2);\n    const c0: number = hostname.charCodeAt(last - 3);\n\n    if (\n      c3 === 109 /* 'm' */ &&\n      c2 === 111 /* 'o' */ &&\n      c1 === 99 /* 'c' */ &&\n      c0 === 46 /* '.' */\n    ) {\n      out.isIcann = true;\n      out.isPrivate = false;\n      out.publicSuffix = 'com';\n      return true;\n    } else if (\n      c3 === 103 /* 'g' */ &&\n      c2 === 114 /* 'r' */ &&\n      c1 === 111 /* 'o' */ &&\n      c0 === 46 /* '.' */\n    ) {\n      out.isIcann = true;\n      out.isPrivate = false;\n      out.publicSuffix = 'org';\n      return true;\n    } else if (\n      c3 === 117 /* 'u' */ &&\n      c2 === 100 /* 'd' */ &&\n      c1 === 101 /* 'e' */ &&\n      c0 === 46 /* '.' */\n    ) {\n      out.isIcann = true;\n      out.isPrivate = false;\n      out.publicSuffix = 'edu';\n      return true;\n    } else if (\n      c3 === 118 /* 'v' */ &&\n      c2 === 111 /* 'o' */ &&\n      c1 === 103 /* 'g' */ &&\n      c0 === 46 /* '.' */\n    ) {\n      out.isIcann = true;\n      out.isPrivate = false;\n      out.publicSuffix = 'gov';\n      return true;\n    } else if (\n      c3 === 116 /* 't' */ &&\n      c2 === 101 /* 'e' */ &&\n      c1 === 110 /* 'n' */ &&\n      c0 === 46 /* '.' */\n    ) {\n      out.isIcann = true;\n      out.isPrivate = false;\n      out.publicSuffix = 'net';\n      return true;\n    } else if (\n      c3 === 101 /* 'e' */ &&\n      c2 === 100 /* 'd' */ &&\n      c1 === 46 /* '.' */\n    ) {\n      out.isIcann = true;\n      out.isPrivate = false;\n      out.publicSuffix = 'de';\n      return true;\n    }\n  }\n\n  return false;\n}\n"], "names": [], "mappings": ";;AAEA;;;;;;;;;;AAUG;AACH,SAAS,qBAAqB,CAAC,QAAgB,EAAE,KAAa,EAAA;AAC5D,IAAA,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;AAC5B,QAAA,QACE,QAAQ,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM;AAChC,YAAA,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG;;AAIxD,IAAA,OAAO,KAAK;AACd;AAEA;;AAEG;AACH,SAAS,uBAAuB,CAC9B,QAAgB,EAChB,YAAoB,EAAA;;;;;;;;;;;;;;;IAgBpB,MAAM,iBAAiB,GAAG,QAAQ,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC;IACnE,MAAM,wBAAwB,GAAG,QAAQ,CAAC,WAAW,CAAC,GAAG,EAAE,iBAAiB,CAAC;;AAG7E,IAAA,IAAI,wBAAwB,KAAK,EAAE,EAAE;AACnC,QAAA,OAAO,QAAQ;;;IAIjB,OAAO,QAAQ,CAAC,KAAK,CAAC,wBAAwB,GAAG,CAAC,CAAC;AACrD;AAEA;;AAEG;AACqB,SAAA,SAAS,CAC/B,MAAc,EACd,QAAgB,EAChB,OAAiB,EAAA;;AAGjB,IAAA,IAAI,OAAO,CAAC,UAAU,KAAK,IAAI,EAAE;AAC/B,QAAA,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU;AACrC,QAAA,KAAK,MAAM,KAAK,IAAI,UAAU,EAAE;YAC9B,oBAAoB,qBAAqB,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE;AAC1D,gBAAA,OAAO,KAAK;;;;IAKlB,IAAI,mBAAmB,GAAG,CAAC;AAC3B,IAAA,IAAI,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;AAC5B,QAAA,OACE,mBAAmB,GAAG,QAAQ,CAAC,MAAM;AACrC,YAAA,QAAQ,CAAC,mBAAmB,CAAC,KAAK,GAAG,EACrC;YACA,mBAAmB,IAAI,CAAC;;;;;;;IAQ5B,IAAI,MAAM,CAAC,MAAM,KAAK,QAAQ,CAAC,MAAM,GAAG,mBAAmB,EAAE;AAC3D,QAAA,OAAO,IAAI;;;;;;;IAQb,uBAAuB,uBAAuB,CAAC,QAAQ,EAAE,MAAM,CAAC;AAClE;;ACnGA;;;;AAIG;AACW,SAAU,sBAAsB,CAC5C,MAAc,EACd,MAAc,EAAA;;;;AAKd,IAAA,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;AAC5C;;ACbA;;;AAGG;AACW,SAAU,eAAe,CACrC,GAAW,EACX,kBAA2B,EAAA;IAE3B,IAAI,KAAK,GAAG,CAAC;AACb,IAAA,IAAI,GAAG,GAAW,GAAG,CAAC,MAAM;IAC5B,IAAI,QAAQ,GAAG,KAAK;;IAGpB,IAAI,CAAC,kBAAkB,EAAE;;AAEvB,QAAA,IAAI,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;AAC3B,YAAA,OAAO,IAAI;;;AAIb,QAAA,OAAO,KAAK,GAAG,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE;YACxD,KAAK,IAAI,CAAC;;;AAIZ,QAAA,OAAO,GAAG,GAAG,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;YACvD,GAAG,IAAI,CAAC;;;QAIV,IACE,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE;AAC5B,YAAA,GAAG,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,EAAE,YAChC;YACA,KAAK,IAAI,CAAC;;aACL;YACL,MAAM,eAAe,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC;AAChD,YAAA,IAAI,eAAe,KAAK,EAAE,EAAE;;;;AAI1B,gBAAA,MAAM,YAAY,GAAG,eAAe,GAAG,KAAK;gBAC5C,MAAM,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC;gBAChC,MAAM,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC;gBACpC,MAAM,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC;gBACpC,MAAM,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC;gBACpC,MAAM,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC;gBAEpC,IACE,YAAY,KAAK,CAAC;oBAClB,EAAE,KAAK,GAAG;oBACV,EAAE,KAAK,GAAG;oBACV,EAAE,KAAK,GAAG;oBACV,EAAE,KAAK,GAAG;AACV,oBAAA,EAAE,KAAK,GAAG,YACV;qBAEK,IACL,YAAY,KAAK,CAAC;oBAClB,EAAE,KAAK,GAAG;oBACV,EAAE,KAAK,GAAG;oBACV,EAAE,KAAK,GAAG;AACV,oBAAA,EAAE,KAAK,GAAG,YACV;qBAEK,IACL,YAAY,KAAK,CAAC;oBAClB,EAAE,KAAK,GAAG;oBACV,EAAE,KAAK,GAAG;AACV,oBAAA,EAAE,KAAK,GAAG,YACV;qBAEK,IACL,YAAY,KAAK,CAAC;oBAClB,EAAE,KAAK,GAAG;AACV,oBAAA,EAAE,KAAK,GAAG,YACV;qBAEK;;AAEL,oBAAA,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,eAAe,EAAE,CAAC,IAAI,CAAC,EAAE;wBAC/C,MAAM,aAAa,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE;AAC5C,wBAAA,IACE,GAEI,CAAC,aAAa,IAAI,EAAE,IAAI,aAAa,IAAI,GAAG;6BAC3C,aAAa,IAAI,EAAE,IAAI,aAAa,IAAI,EAAE,CAAC;4BAC5C,aAAa,KAAK,EAAE;4BACpB,aAAa,KAAK,EAAE;AACpB,4BAAA,aAAa,KAAK,EAAE;AAEvB,yBAAA,EACD;AACA,4BAAA,OAAO,IAAI;;;;;AAMjB,gBAAA,KAAK,GAAG,eAAe,GAAG,CAAC;gBAC3B,OAAO,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,YAAY;oBAC7C,KAAK,IAAI,CAAC;;;;;;;AAQhB,QAAA,IAAI,iBAAiB,GAAG,EAAE;AAC1B,QAAA,IAAI,qBAAqB,GAAG,EAAE;AAC9B,QAAA,IAAI,WAAW,GAAG,EAAE;AACpB,QAAA,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE;YACnC,MAAM,IAAI,GAAW,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;AACtC,YAAA,IACE,IAAI,KAAK,EAAE;gBACX,IAAI,KAAK,EAAE;gBACX,IAAI,KAAK,EAAE;cACX;gBACA,GAAG,GAAG,CAAC;gBACP;;AACK,iBAAA,IAAI,IAAI,KAAK,EAAE,EAAE;;gBAEtB,iBAAiB,GAAG,CAAC;;AAChB,iBAAA,IAAI,IAAI,KAAK,EAAE,EAAE;;gBAEtB,qBAAqB,GAAG,CAAC;;AACpB,iBAAA,IAAI,IAAI,KAAK,EAAE,EAAE;;gBAEtB,WAAW,GAAG,CAAC;;iBACV,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE,EAAE;gBACnC,QAAQ,GAAG,IAAI;;;;QAKnB,IACE,iBAAiB,KAAK,EAAE;AACxB,YAAA,iBAAiB,GAAG,KAAK;YACzB,iBAAiB,GAAG,GAAG,EACvB;AACA,YAAA,KAAK,GAAG,iBAAiB,GAAG,CAAC;;;QAI/B,IAAI,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,YAAY;AAC1C,YAAA,IAAI,qBAAqB,KAAK,EAAE,EAAE;AAChC,gBAAA,OAAO,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,qBAAqB,CAAC,CAAC,WAAW,EAAE;;AAElE,YAAA,OAAO,IAAI;;AACN,aAAA,IAAI,WAAW,KAAK,EAAE,IAAI,WAAW,GAAG,KAAK,IAAI,WAAW,GAAG,GAAG,EAAE;;YAEzE,GAAG,GAAG,WAAW;;;;AAKrB,IAAA,OAAO,GAAG,GAAG,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,EAAE,YAAY;QAClE,GAAG,IAAI,CAAC;;IAGV,MAAM,QAAQ,GACZ,KAAK,KAAK,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,GAAG;IAEjE,IAAI,QAAQ,EAAE;AACZ,QAAA,OAAO,QAAQ,CAAC,WAAW,EAAE;;AAG/B,IAAA,OAAO,QAAQ;AACjB;;ACzKA;;;AAGG;AACH,SAAS,cAAc,CAAC,QAAgB,EAAA;;AAEtC,IAAA,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;AACvB,QAAA,OAAO,KAAK;;;AAId,IAAA,IAAI,QAAQ,CAAC,MAAM,GAAG,EAAE,EAAE;AACxB,QAAA,OAAO,KAAK;;IAGd,IAAI,YAAY,GAAG,CAAC;AAEpB,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;QAC3C,MAAM,IAAI,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;AAEnC,QAAA,IAAI,IAAI,KAAK,EAAE,YAAY;YACzB,YAAY,IAAI,CAAC;;AACZ,aAAA,IAAI,IAAI,GAAG,EAAE,cAAc,IAAI,GAAG,EAAE,YAAY;AACrD,YAAA,OAAO,KAAK;;;IAIhB,QACE,YAAY,KAAK,CAAC;QAClB,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE;AAC7B,QAAA,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE;AAEnD;AAEA;;AAEG;AACH,SAAS,cAAc,CAAC,QAAgB,EAAA;AACtC,IAAA,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;AACvB,QAAA,OAAO,KAAK;;AAGd,IAAA,IAAI,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;AAC5C,IAAA,IAAI,GAAG,GAAG,QAAQ,CAAC,MAAM;IAEzB,IAAI,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;QAC7B,GAAG,IAAI,CAAC;;;;;AAMV,IAAA,IAAI,GAAG,GAAG,KAAK,GAAG,EAAE,EAAE;AACpB,QAAA,OAAO,KAAK;;IAGd,IAAI,QAAQ,GAAG,KAAK;IAEpB,OAAO,KAAK,GAAG,GAAG,EAAE,KAAK,IAAI,CAAC,EAAE;QAC9B,MAAM,IAAI,GAAG,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC;AAEvC,QAAA,IAAI,IAAI,KAAK,EAAE,YAAY;YACzB,QAAQ,GAAG,IAAI;;AACV,aAAA,IACL,GAEI,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE;aACxB,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,GAAG,CAAC;aAC1B,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE,CAAC;AAE7B,SAAA,EACD;AACA,YAAA,OAAO,KAAK;;;AAIhB,IAAA,OAAO,QAAQ;AACjB;AAEA;;;;AAIG;AACqB,SAAA,IAAI,CAAC,QAAgB,EAAA;IAC3C,OAAO,cAAc,CAAC,QAAQ,CAAC,IAAI,cAAc,CAAC,QAAQ,CAAC;AAC7D;;ACtFA;;;;;;;AAOG;AAEH,SAAS,YAAY,CAAC,IAAY,EAAA;IAChC,QACE,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,GAAG,MAAM,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,GAAG,GAAG;AAE3E;AAEA;;;;;AAKG;AACW,wBAAA,EAAW,QAAgB,EAAA;AACvC,IAAA,IAAI,QAAQ,CAAC,MAAM,GAAG,GAAG,EAAE;AACzB,QAAA,OAAO,KAAK;;AAGd,IAAA,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;AACzB,QAAA,OAAO,KAAK;;AAGd,IAAA;oBACkB,CAAC,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACrD,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE;QAC7B,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE;MAC7B;AACA,QAAA,OAAO,KAAK;;;AAId,IAAA,IAAI,YAAY,GAAG,EAAE;AACrB,IAAA,IAAI,YAAY,GAAG,EAAE;AACrB,IAAA,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM;AAE3B,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE;QAC/B,MAAM,IAAI,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;AACnC,QAAA,IAAI,IAAI,KAAK,EAAE,YAAY;AACzB,YAAA;;YAEE,CAAC,GAAG,YAAY,GAAG,EAAE;;AAErB,gBAAA,YAAY,KAAK,EAAE;;AAEnB,gBAAA,YAAY,KAAK,EAAE;;gBAEnB,YAAY,KAAK,EAAE,EACnB;AACA,gBAAA,OAAO,KAAK;;YAGd,YAAY,GAAG,CAAC;;AACX,aAAA,IACL,mBAAmB,YAAY,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,EAAE,IAAI,IAAI,KAAK,EAAE,EAAE,EACrE;;AAEA,YAAA,OAAO,KAAK;;QAGd,YAAY,GAAG,IAAI;;IAGrB;;AAEE,IAAA,GAAG,GAAG,YAAY,GAAG,CAAC,IAAI,EAAE;;;;QAI5B,YAAY,KAAK,EAAE;AAEvB;;ACpEA,SAAS,eAAe,CAAC,EACvB,iBAAiB,GAAG,IAAI,EACxB,mBAAmB,GAAG,KAAK,EAC3B,QAAQ,GAAG,IAAI,EACf,eAAe,GAAG,IAAI,EACtB,WAAW,GAAG,IAAI,EAClB,UAAU,GAAG,IAAI,EACjB,gBAAgB,GAAG,IAAI,GACL,EAAA;IAClB,OAAO;QACL,iBAAiB;QACjB,mBAAmB;QACnB,QAAQ;QACR,eAAe;QACf,WAAW;QACX,UAAU;QACV,gBAAgB;KACjB;AACH;AAEA,MAAM,eAAe,mBAAmB,eAAe,CAAC,EAAE,CAAC;AAErD,SAAU,WAAW,CAAC,OAA2B,EAAA;AACrD,IAAA,IAAI,OAAO,KAAK,SAAS,EAAE;AACzB,QAAA,OAAO,eAAe;;AAGxB,IAAA,uBAAuB,eAAe,CAAC,OAAO,CAAC;AACjD;;ACtCA;;AAEG;AACW,SAAU,YAAY,CAAC,QAAgB,EAAE,MAAc,EAAA;;IAEnE,IAAI,MAAM,CAAC,MAAM,KAAK,QAAQ,CAAC,MAAM,EAAE;AACrC,QAAA,OAAO,EAAE;;AAGX,IAAA,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;AAC9C;;ACVA;;;;AAIG;SAgCa,cAAc,GAAA;IAC5B,OAAO;AACL,QAAA,MAAM,EAAE,IAAI;AACZ,QAAA,mBAAmB,EAAE,IAAI;AACzB,QAAA,QAAQ,EAAE,IAAI;AACd,QAAA,OAAO,EAAE,IAAI;AACb,QAAA,IAAI,EAAE,IAAI;AACV,QAAA,SAAS,EAAE,IAAI;AACf,QAAA,YAAY,EAAE,IAAI;AAClB,QAAA,SAAS,EAAE,IAAI;KAChB;AACH;AAEM,SAAU,WAAW,CAAC,MAAe,EAAA;AACzC,IAAA,MAAM,CAAC,MAAM,GAAG,IAAI;AACpB,IAAA,MAAM,CAAC,mBAAmB,GAAG,IAAI;AACjC,IAAA,MAAM,CAAC,QAAQ,GAAG,IAAI;AACtB,IAAA,MAAM,CAAC,OAAO,GAAG,IAAI;AACrB,IAAA,MAAM,CAAC,IAAI,GAAG,IAAI;AAClB,IAAA,MAAM,CAAC,SAAS,GAAG,IAAI;AACvB,IAAA,MAAM,CAAC,YAAY,GAAG,IAAI;AAC1B,IAAA,MAAM,CAAC,SAAS,GAAG,IAAI;AACzB;AAeM,SAAU,SAAS,CACvB,GAAW,EACX,IAAU,EACV,YAIS,EACT,cAAiC,EACjC,MAAe,EAAA;IAEf,MAAM,OAAO,mBAA6B,WAAW,CAAC,cAAc,CAAC;;;;AAKrE,IAAA,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;AAC3B,QAAA,OAAO,MAAM;;;;;;;;;;;;AAaf,IAAA,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;AAC5B,QAAA,MAAM,CAAC,QAAQ,GAAG,GAAG;;AAChB,SAAA,IAAI,OAAO,CAAC,WAAW,EAAE;AAC9B,QAAA,MAAM,CAAC,QAAQ,GAAG,eAAe,CAAC,GAAG,EAAE,eAAe,CAAC,GAAG,CAAC,CAAC;;SACvD;QACL,MAAM,CAAC,QAAQ,GAAG,eAAe,CAAC,GAAG,EAAE,KAAK,CAAC;;IAG/C,IAAI,IAAI,8BAAsB,MAAM,CAAC,QAAQ,KAAK,IAAI,EAAE;AACtD,QAAA,OAAO,MAAM;;;AAIf,IAAA,IAAI,OAAO,CAAC,QAAQ,EAAE;QACpB,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;AACnC,QAAA,IAAI,MAAM,CAAC,IAAI,EAAE;AACf,YAAA,OAAO,MAAM;;;;;IAMjB,IACE,OAAO,CAAC,gBAAgB;AACxB,QAAA,OAAO,CAAC,eAAe;AACvB,QAAA,CAAC,eAAe,CAAC,MAAM,CAAC,QAAQ,CAAC,EACjC;AACA,QAAA,MAAM,CAAC,QAAQ,GAAG,IAAI;AACtB,QAAA,OAAO,MAAM;;;IAIf,YAAY,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC;IAC9C,IAAI,IAAI,mCAA2B,MAAM,CAAC,YAAY,KAAK,IAAI,EAAE;AAC/D,QAAA,OAAO,MAAM;;;AAIf,IAAA,MAAM,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,YAAY,EAAE,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC;IACxE,IAAI,IAAI,4BAAoB,MAAM,CAAC,MAAM,KAAK,IAAI,EAAE;AAClD,QAAA,OAAO,MAAM;;;AAIf,IAAA,MAAM,CAAC,SAAS,GAAG,YAAY,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC;IAC/D,IAAI,IAAI,KAAoB,CAAA,wBAAE;AAC5B,QAAA,OAAO,MAAM;;;AAIf,IAAA,MAAM,CAAC,mBAAmB,GAAG,sBAAsB,CACjD,MAAM,CAAC,MAAM,EACb,MAAM,CAAC,YAAY,CACpB;AAED,IAAA,OAAO,MAAM;AACf;;AC7Jc,iBAAA,EACZ,QAAgB,EAChB,OAA6B,EAC7B,GAAkB,EAAA;;;IAIlB,IAAI,CAAC,OAAO,CAAC,mBAAmB,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;AACvD,QAAA,MAAM,IAAI,GAAW,QAAQ,CAAC,MAAM,GAAG,CAAC;QACxC,MAAM,EAAE,GAAW,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC;QAC5C,MAAM,EAAE,GAAW,QAAQ,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,CAAC;QAChD,MAAM,EAAE,GAAW,QAAQ,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,CAAC;QAChD,MAAM,EAAE,GAAW,QAAQ,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,CAAC;AAEhD,QAAA,IACE,EAAE,KAAK,GAAG;YACV,EAAE,KAAK,GAAG;YACV,EAAE,KAAK,EAAE;AACT,YAAA,EAAE,KAAK,EAAE,YACT;AACA,YAAA,GAAG,CAAC,OAAO,GAAG,IAAI;AAClB,YAAA,GAAG,CAAC,SAAS,GAAG,KAAK;AACrB,YAAA,GAAG,CAAC,YAAY,GAAG,KAAK;AACxB,YAAA,OAAO,IAAI;;AACN,aAAA,IACL,EAAE,KAAK,GAAG;YACV,EAAE,KAAK,GAAG;YACV,EAAE,KAAK,GAAG;AACV,YAAA,EAAE,KAAK,EAAE,YACT;AACA,YAAA,GAAG,CAAC,OAAO,GAAG,IAAI;AAClB,YAAA,GAAG,CAAC,SAAS,GAAG,KAAK;AACrB,YAAA,GAAG,CAAC,YAAY,GAAG,KAAK;AACxB,YAAA,OAAO,IAAI;;AACN,aAAA,IACL,EAAE,KAAK,GAAG;YACV,EAAE,KAAK,GAAG;YACV,EAAE,KAAK,GAAG;AACV,YAAA,EAAE,KAAK,EAAE,YACT;AACA,YAAA,GAAG,CAAC,OAAO,GAAG,IAAI;AAClB,YAAA,GAAG,CAAC,SAAS,GAAG,KAAK;AACrB,YAAA,GAAG,CAAC,YAAY,GAAG,KAAK;AACxB,YAAA,OAAO,IAAI;;AACN,aAAA,IACL,EAAE,KAAK,GAAG;YACV,EAAE,KAAK,GAAG;YACV,EAAE,KAAK,GAAG;AACV,YAAA,EAAE,KAAK,EAAE,YACT;AACA,YAAA,GAAG,CAAC,OAAO,GAAG,IAAI;AAClB,YAAA,GAAG,CAAC,SAAS,GAAG,KAAK;AACrB,YAAA,GAAG,CAAC,YAAY,GAAG,KAAK;AACxB,YAAA,OAAO,IAAI;;AACN,aAAA,IACL,EAAE,KAAK,GAAG;YACV,EAAE,KAAK,GAAG;YACV,EAAE,KAAK,GAAG;AACV,YAAA,EAAE,KAAK,EAAE,YACT;AACA,YAAA,GAAG,CAAC,OAAO,GAAG,IAAI;AAClB,YAAA,GAAG,CAAC,SAAS,GAAG,KAAK;AACrB,YAAA,GAAG,CAAC,YAAY,GAAG,KAAK;AACxB,YAAA,OAAO,IAAI;;AACN,aAAA,IACL,EAAE,KAAK,GAAG;YACV,EAAE,KAAK,GAAG;AACV,YAAA,EAAE,KAAK,EAAE,YACT;AACA,YAAA,GAAG,CAAC,OAAO,GAAG,IAAI;AAClB,YAAA,GAAG,CAAC,SAAS,GAAG,KAAK;AACrB,YAAA,GAAG,CAAC,YAAY,GAAG,IAAI;AACvB,YAAA,OAAO,IAAI;;;AAIf,IAAA,OAAO,KAAK;AACd;;;;;;;;"}
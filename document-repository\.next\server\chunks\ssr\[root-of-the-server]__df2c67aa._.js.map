{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/management/document-repository/src/components/SessionProvider.tsx"], "sourcesContent": ["'use client'\n\nimport { SessionProvider as NextAuthSessionProvider } from 'next-auth/react'\n\nexport default function SessionProvider({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  return <NextAuthSessionProvider>{children}</NextAuthSessionProvider>\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS,gBAAgB,EACtC,QAAQ,EAGT;IACC,qBAAO,8OAAC,qIAAA,CAAA,kBAAuB;kBAAE;;;;;;AACnC", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/management/document-repository/src/components/Navbar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useSession, signIn, signOut } from 'next-auth/react'\nimport { Menu, X, FileText, Upload, Settings, LogOut, User } from 'lucide-react'\nimport Link from 'next/link'\n\nexport default function Navbar() {\n  const { data: session, status } = useSession()\n  const [isMenuOpen, setIsMenuOpen] = useState(false)\n\n  if (status === 'loading') {\n    return (\n      <nav className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between h-16\">\n            <div className=\"flex items-center\">\n              <div className=\"animate-pulse bg-gray-200 h-8 w-32 rounded\"></div>\n            </div>\n          </div>\n        </div>\n      </nav>\n    )\n  }\n\n  if (!session) {\n    return (\n      <nav className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between h-16\">\n            <div className=\"flex items-center\">\n              <FileText className=\"h-8 w-8 text-blue-600 mr-2\" />\n              <span className=\"text-xl font-bold text-gray-900\">Document Repository</span>\n            </div>\n            <div className=\"flex items-center\">\n              <button\n                onClick={() => signIn('google')}\n                className=\"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors\"\n              >\n                Sign In with Google\n              </button>\n            </div>\n          </div>\n        </div>\n      </nav>\n    )\n  }\n\n  return (\n    <nav className=\"bg-white shadow-sm border-b border-gray-200\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          {/* Logo and Brand */}\n          <div className=\"flex items-center\">\n            <FileText className=\"h-8 w-8 text-blue-600 mr-2\" />\n            <Link href=\"/\" className=\"text-xl font-bold text-gray-900\">\n              Document Repository\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <Link\n              href=\"/\"\n              className=\"text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n            >\n              Documents\n            </Link>\n            <Link\n              href=\"/upload\"\n              className=\"text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center\"\n            >\n              <Upload className=\"h-4 w-4 mr-1\" />\n              Upload\n            </Link>\n            {session.user.role === 'ADMIN' && (\n              <Link\n                href=\"/admin\"\n                className=\"text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center\"\n              >\n                <Settings className=\"h-4 w-4 mr-1\" />\n                Admin\n              </Link>\n            )}\n            \n            {/* User Menu */}\n            <div className=\"relative\">\n              <div className=\"flex items-center space-x-3\">\n                {session.user.image && (\n                  <img\n                    src={session.user.image}\n                    alt={session.user.name || 'User'}\n                    className=\"h-8 w-8 rounded-full\"\n                  />\n                )}\n                <div className=\"hidden lg:block\">\n                  <div className=\"text-sm font-medium text-gray-900\">\n                    {session.user.name}\n                  </div>\n                  <div className=\"text-xs text-gray-500\">\n                    {session.user.role}\n                  </div>\n                </div>\n                <button\n                  onClick={() => signOut()}\n                  className=\"text-gray-400 hover:text-gray-600 p-1\"\n                  title=\"Sign out\"\n                >\n                  <LogOut className=\"h-4 w-4\" />\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden flex items-center\">\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"text-gray-400 hover:text-gray-600 p-2\"\n            >\n              {isMenuOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t border-gray-200\">\n              <Link\n                href=\"/\"\n                className=\"text-gray-700 hover:text-blue-600 block px-3 py-2 rounded-md text-base font-medium\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Documents\n              </Link>\n              <Link\n                href=\"/upload\"\n                className=\"text-gray-700 hover:text-blue-600 block px-3 py-2 rounded-md text-base font-medium\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Upload\n              </Link>\n              {session.user.role === 'ADMIN' && (\n                <Link\n                  href=\"/admin\"\n                  className=\"text-gray-700 hover:text-blue-600 block px-3 py-2 rounded-md text-base font-medium\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  Admin\n                </Link>\n              )}\n              <div className=\"border-t border-gray-200 pt-4 pb-3\">\n                <div className=\"flex items-center px-3\">\n                  {session.user.image && (\n                    <img\n                      src={session.user.image}\n                      alt={session.user.name || 'User'}\n                      className=\"h-10 w-10 rounded-full\"\n                    />\n                  )}\n                  <div className=\"ml-3\">\n                    <div className=\"text-base font-medium text-gray-800\">\n                      {session.user.name}\n                    </div>\n                    <div className=\"text-sm font-medium text-gray-500\">\n                      {session.user.email}\n                    </div>\n                  </div>\n                </div>\n                <div className=\"mt-3 px-2\">\n                  <button\n                    onClick={() => signOut()}\n                    className=\"block w-full text-left px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600\"\n                  >\n                    Sign out\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,IAAI,WAAW,WAAW;QACxB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;IAM3B;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;sCAEpD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,CAAA,GAAA,qIAAA,CAAA,SAAM,AAAD,EAAE;gCACtB,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQb;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAkC;;;;;;;;;;;;sCAM7D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;gCAGpC,QAAQ,IAAI,CAAC,IAAI,KAAK,yBACrB,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAMzC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;4CACZ,QAAQ,IAAI,CAAC,KAAK,kBACjB,8OAAC;gDACC,KAAK,QAAQ,IAAI,CAAC,KAAK;gDACvB,KAAK,QAAQ,IAAI,CAAC,IAAI,IAAI;gDAC1B,WAAU;;;;;;0DAGd,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,QAAQ,IAAI,CAAC,IAAI;;;;;;kEAEpB,8OAAC;wDAAI,WAAU;kEACZ,QAAQ,IAAI,CAAC,IAAI;;;;;;;;;;;;0DAGtB,8OAAC;gDACC,SAAS,IAAM,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;gDACrB,WAAU;gDACV,OAAM;0DAEN,cAAA,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO1B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAU;0CAET,2BAAa,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAAe,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;gBAM/D,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;4BAGA,QAAQ,IAAI,CAAC,IAAI,KAAK,yBACrB,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAIH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;4CACZ,QAAQ,IAAI,CAAC,KAAK,kBACjB,8OAAC;gDACC,KAAK,QAAQ,IAAI,CAAC,KAAK;gDACvB,KAAK,QAAQ,IAAI,CAAC,IAAI,IAAI;gDAC1B,WAAU;;;;;;0DAGd,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,QAAQ,IAAI,CAAC,IAAI;;;;;;kEAEpB,8OAAC;wDAAI,WAAU;kEACZ,QAAQ,IAAI,CAAC,KAAK;;;;;;;;;;;;;;;;;;kDAIzB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,SAAS,IAAM,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;4CACrB,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}]}
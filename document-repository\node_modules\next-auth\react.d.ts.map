{"version": 3, "file": "react.d.ts", "sourceRoot": "", "sources": ["src/react.tsx"], "names": [], "mappings": "AAAA;;;;;;;;;;GAUG;AAIH,OAAO,KAAK,KAAK,MAAM,OAAO,CAAA;AAU9B,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAA;AACtD,OAAO,KAAK,EAAkB,OAAO,EAAE,MAAM,kBAAkB,CAAA;AAC/D,OAAO,KAAK,EACV,gBAAgB,EAChB,kBAAkB,EAClB,oBAAoB,EACpB,yBAAyB,EACzB,aAAa,EACb,cAAc,EACd,aAAa,EACb,eAAe,EACf,iBAAiB,EAClB,MAAM,iBAAiB,CAAA;AAGxB,YAAY,EACV,aAAa,EACb,yBAAyB,EACzB,aAAa,EACb,cAAc,GACf,CAAA;AAED,OAAO,EAAE,oBAAoB,EAAE,CAAA;AAO/B,eAAO,MAAM,UAAU,EAAE,gBAcxB,CAAA;AAqCD,qBAAqB;AACrB,MAAM,MAAM,aAAa,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,KAAK,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,CAAA;AAEnE;;GAEG;AACH,MAAM,MAAM,mBAAmB,CAAC,CAAC,SAAS,OAAO,GAAG,KAAK,IAAI,CAAC,SAAS,IAAI,GAEnE;IAAE,MAAM,EAAE,aAAa,CAAC;IAAC,IAAI,EAAE,OAAO,CAAC;IAAC,MAAM,EAAE,eAAe,CAAA;CAAE,GACjE;IAAE,MAAM,EAAE,aAAa,CAAC;IAAC,IAAI,EAAE,IAAI,CAAC;IAAC,MAAM,EAAE,SAAS,CAAA;CAAE,GAExD;IAAE,MAAM,EAAE,aAAa,CAAC;IAAC,IAAI,EAAE,OAAO,CAAC;IAAC,MAAM,EAAE,eAAe,CAAA;CAAE,GACjE;IACE,MAAM,EAAE,aAAa,CAAA;IACrB,IAAI,EAAE,IAAI,CAAA;IACV,MAAM,EAAE,iBAAiB,GAAG,SAAS,CAAA;CACtC,CAAA;AAET,eAAO,MAAM,cAAc;YAPT,aAAa;UAAQ,OAAO;YAAU,eAAe;;YAErD,aAAa;UACf,IAAI;YACF,iBAAiB,GAAG,SAAS;cAKnC,CAAA;AAEZ;;;;;;GAMG;AACH,wBAAgB,UAAU,CAAC,CAAC,SAAS,OAAO,EAC1C,OAAO,CAAC,EAAE,iBAAiB,CAAC,CAAC,CAAC,GAC7B,mBAAmB,CAAC,CAAC,CAAC,CAqCxB;AAED,MAAM,WAAW,gBAAgB;IAC/B,KAAK,CAAC,EAAE,SAAS,GAAG,OAAO,GAAG,QAAQ,GAAG,MAAM,CAAA;IAC/C,YAAY,CAAC,EAAE,OAAO,CAAA;IACtB,SAAS,CAAC,EAAE,OAAO,CAAA;CACpB;AAED,wBAAsB,UAAU,CAAC,MAAM,CAAC,EAAE,gBAAgB,2BAezD;AAED;;;;;GAKG;AACH,wBAAsB,YAAY,oBAOjC;AAED,wBAAsB,YAAY,2DAMjC;AAED;;;;;;GAMG;AACH,wBAAsB,MAAM,CAC1B,QAAQ,CAAC,EAAE,UAAU,EACrB,OAAO,CAAC,EAAE,aAAa,CAAC,IAAI,CAAC,EAC7B,mBAAmB,CAAC,EAAE,yBAAyB,GAC9C,OAAO,CAAC,IAAI,CAAC,CAAA;AAChB,wBAAsB,MAAM,CAC1B,QAAQ,CAAC,EAAE,UAAU,EACrB,OAAO,CAAC,EAAE,aAAa,CAAC,KAAK,CAAC,EAC9B,mBAAmB,CAAC,EAAE,yBAAyB,GAC9C,OAAO,CAAC,cAAc,CAAC,CAAA;AAyF1B;;;;;;GAMG;AACH,wBAAsB,OAAO,CAAC,OAAO,CAAC,EAAE,aAAa,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;AAC3E,wBAAsB,OAAO,CAC3B,OAAO,CAAC,EAAE,aAAa,CAAC,KAAK,CAAC,GAC7B,OAAO,CAAC,eAAe,CAAC,CAAA;AAoC3B;;;;;;;;;GASG;AACH,wBAAgB,eAAe,CAAC,KAAK,EAAE,oBAAoB,eAgK1D"}
{"name": "document-repository", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "setup": "node setup.js", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:reset": "prisma migrate reset"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@prisma/client": "^6.12.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@types/multer": "^2.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "googleapis": "^153.0.0", "lucide-react": "^0.525.0", "multer": "^2.0.2", "next": "15.4.2", "next-auth": "^5.0.0-beta.29", "prisma": "^6.12.0", "react": "19.1.0", "react-dom": "19.1.0", "react-dropzone": "^14.3.8", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.2", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "tailwindcss": "^4", "ts-jest": "^29.4.0", "typescript": "^5"}}